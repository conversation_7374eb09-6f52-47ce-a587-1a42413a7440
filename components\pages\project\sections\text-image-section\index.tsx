import React from "react";
import TextEditor from "../text-section/text-editor";
import ImageSection from "../image-section";
import { Section, Text, ExternalLink, Image as Images } from "@prisma/client";

type Props = {
  section: Section & {
    text?: (Text & { externalLink?: ExternalLink | null }) | null;
    externalLink?: ExternalLink | null;
    image?: Images | null;
  };
};

const TextImageSection = ({ section }: Props) => {
  return (
    <>
      <div className="flex flex-col md:flex-row gap-4">
        {section.text && (
          <TextEditor sectionId={section.id} text={section.text} />
        )}
        <ImageSection section={section} />
      </div>
    </>
  );
};

export default TextImageSection;
