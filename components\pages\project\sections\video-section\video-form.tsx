"use client";

import React, { useState } from "react";
import { Section, Video } from "@prisma/client";
import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>ger,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import Form from "next/form";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { updateVideoAction } from "@/actions/video";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";

type Props = {
  children?: React.ReactNode;
  sectionId: Section["id"];
  video?: Video;
};

const VideoForm = ({ children, sectionId, video }: Props) => {
  const [src, setSrc] = useState(video?.src || "");
  const [alt, setAlt] = useState(video?.alt || "");
  const [caption, setCaption] = useState(video?.caption || "");
  const [width, setWidth] = useState(video?.width || "");
  const [height, setHeight] = useState(video?.height || "");

  const handleUpdateVideo = async () => {
    const { data, message, error } = await updateVideoAction({
      id: video?.id as string,
      src,
      alt,
      caption,
      width,
      height,
    });

    if (data) {
      toast.success(message);
    }

    if (error) {
      toast.error(message);
    }
  };

  return (
    <>
      <Dialog>
        <DialogTrigger asChild>{children}</DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change Video</DialogTitle>
            <DialogDescription>
              Update the video URL, alt, caption, width, and height.
            </DialogDescription>
          </DialogHeader>
          <Form className="space-y-3" action={handleUpdateVideo}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2 grid col-span-2 ">
                <Label htmlFor="src">Video Source URL</Label>
                <Textarea
                  id="src"
                  placeholder="Video Source URL"
                  className="peer text-xs"
                  value={src || ""}
                  onChange={(e) => setSrc(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="alt">Video Alt</Label>
                <Textarea
                  id="alt"
                  placeholder="Video Alt"
                  className="peer text-xs"
                  value={alt || ""}
                  onChange={(e) => setAlt(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="caption">Video Caption</Label>
                <Textarea
                  id="caption"
                  placeholder="Video Caption"
                  className="peer text-xs"
                  value={caption || ""}
                  onChange={(e) => setCaption(e.target.value)}
                />
              </div>
            </div>

            <div className="flex gap-2">
              <div className="space-y-2 w-1/2">
                <Label htmlFor="width">Video Width</Label>
                <Input
                  type="number"
                  id="width"
                  placeholder="Video Width"
                  className="peer text-xs w-1/2"
                  value={width || ""}
                  onChange={(e) => setWidth(e.target.value)}
                />
                <p className="text-muted-foreground text-sm">
                  Video width in pixels
                </p>
              </div>
              <div className="space-y-2 w-1/2">
                <Label htmlFor="height">Video Height</Label>
                <Input
                  type="number"
                  id="height"
                  placeholder="Video Height"
                  className="peer text-xs w-1/2"
                  value={height || ""}
                  onChange={(e) => setHeight(e.target.value)}
                />
                <p className="text-muted-foreground text-sm">
                  Video height in pixels
                </p>
              </div>
            </div>

            <Button type="submit">Update</Button>
          </Form>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default VideoForm;
