"use server";

import db from "@/lib/db";

export const createVideo = async (data: any) => {
  try {
    const video = await db.video.create({
      data: {
        sectionId: data.sectionId,
        src: data.src,
        alt: data.alt,
        caption: data.caption,
        width: data.width,
        height: data.height,
      },
      include: {
        section: {
          select: {
            projectId: true,
          },
          include: {
            project: true,
            text: true,
            image: true,
            externalLink: true,
            textImage: true,
            video: true,
            gallery: true,
            social: true,
            map: true,
          },
        },
      },
    });

    return video;
  } catch (error) {
    console.error("Error creating video:", error);
    return;
  }
};

export const updateVideo = async (data: any) => {
  try {
    const video = await db.video.update({
      where: {
        id: data.id,
      },
      data: {
        src: data.src,
        alt: data.alt,
        caption: data.caption,
        width: data.width,
        height: data.height,
      },
      include: {
        section: {
          select: {
            projectId: true,
          },
        },
      },
    });

    return video;
  } catch (error) {
    console.error("Error updating video:", error);
    return;
  }
};

export const isVideo = async (id: string) => {
  const video = await db.video.findUnique({
    where: {
      id,
    },
    include: {
      section: {
        select: {
          projectId: true,
          project: true,
          text: true,
          image: true,
          externalLink: true,
          textImage: true,
          video: true,
          gallery: true,
          social: true,
          map: true,
        },
      },
    },
  });

  return video;
};

export const deleteVideo = async (id: string) => {
  const video = await db.video.delete({
    where: {
      id,
    },
    include: {
      section: {
        select: {
          projectId: true,
        },
      },
    },
  });

  return video;
};
