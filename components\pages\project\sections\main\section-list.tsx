import React from "react";
import SectionButton from "./section-button";
import { $Enums, Project, Section, Text } from "@prisma/client";
import TextEditor from "../text-section/text-editor";
import SectionContainers from "./section-containers";
import { ProjectWithAll } from "@/lib/types";
import ImageSection from "../image-section";
import TextImageSection from "../text-image-section";
import VideoSection from "../video-section";
import GallarySection from "../gallary-section";

type Props = {
  sections: ProjectWithAll["sections"];
  projectId: Project["id"];
};

const SectionList = ({ sections, projectId }: Props) => {
  return (
    <div className="space-y-1">
      {/* Welcome message for empty state */}
      {sections.length === 0 && (
        <div className="text-center py-16 px-8">
          <div className="">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full" />
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-2">
              Start Building Your Website
            </h3>
            <p className="text-muted-foreground mb-6">
              Add your first section to begin creating your amazing website.
              Choose from text, images, videos, and more.
            </p>
          </div>
        </div>
      )}

      <ul className="space-y-0">
        {sections.map((section) => (
          <li key={section.id} className="group">
            <SectionButton index={section.index} projectId={projectId} />
            <SectionContainers key={section.id} section={section}>
              {section.type === $Enums.SectionType.TEXT && section.text && (
                <TextEditor sectionId={section.id} text={section.text} />
              )}
              {section.type === $Enums.SectionType.IMAGE && (
                <ImageSection section={section} />
              )}
              {section.type === $Enums.SectionType.TEXTIMAGE && (
                <TextImageSection section={section} />
              )}
              {section.type === $Enums.SectionType.VIDEO && (
                <VideoSection section={section} />
              )}
              {section.type === $Enums.SectionType.GALLERY && (
                <GallarySection section={section} />
              )}
            </SectionContainers>
          </li>
        ))}

        <li className="group">
          <SectionButton index={sections.length} projectId={projectId} />
        </li>
      </ul>
    </div>
  );
};

export default SectionList;
