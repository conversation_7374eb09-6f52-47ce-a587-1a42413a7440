// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum SectionType {
  VIDEO
  IMAGE
  TEXT
  TEXTIMAGE
  FORM
  SOCIAL
  MAP
  ECOMMERCE
  GALLERY
}

enum ExternalLinkType {
  SECTION
  TEXT
}

enum RowPosition {
  RIGHT
  CENTER
  LEFT
}

model User {
  id        String    @id @default(uuid())
  name      String
  email     String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  Project   Project[]

  @@map("users")
}

model Project {
  id        String    @id @default(uuid())
  title     String
  userId    String
  user      User      @relation(fields: [userId], references: [id])
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  sections  Section[]

  @@index([userId, createdAt(sort: Desc)])
  @@map("projects")
}

model Section {
  id           String        @id @default(uuid())
  title        String?
  projectId    String
  index        Int
  project      Project       @relation(fields: [projectId], references: [id], onDelete: Cascade)
  type         SectionType
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  text         Text?
  externalLink ExternalLink?
  image        Image?
  video        Video?
  textImage    TextImage?
  social       Social[]
  map          Map?
  gallery      Gallery?
  // Form         Form?
  // Ecommerce    Ecommerce?

  @@index([projectId, index(sort: Asc)])
  @@map("sections")
}

model Text {
  id           String        @id @default(uuid())
  content      String        @default("<h1> This is you heading </h1> <p> you can write as much as you want here. you can write a long paragraph or you can write a short paragraph. click here to check out. </p>")
  sectionId    String        @unique @map("sectionId")
  rowPosition  RowPosition   @default(CENTER)
  section      Section       @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  externalLink ExternalLink?
  textImage    TextImage?

  @@index([sectionId])
  @@map("texts")
}

model ExternalLink {
  id          String            @id @default(uuid())
  label       String?           @default("Click Me!")
  url         String            @default("")
  type        ExternalLinkType? @default(TEXT)
  rowPosition RowPosition?      @default(CENTER)
  section     Section?          @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  sectionId   String?           @unique @map("sectionId")
  text        Text?             @relation(fields: [textId], references: [id], onDelete: Cascade)
  textId      String?           @unique @map("textId")
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  @@map("external_links")
}

model Image {
  id           String        @id @default(uuid())
  src          String        @default("https://images.unsplash.com/photo-1702834000621-76c4a9d15868?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D")
  alt          String        @default("Placeholder Image")
  caption      String?
  width        String?
  height       String?
  sectionId    String        @unique @map("sectionId")
  section      Section       @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  textImage    TextImage?
  Gallery      Gallery?      @relation(fields: [galleryId], references: [id])
  galleryId    String?
  GalleryImage GalleryImage?
  galleries    Gallery[]     @relation("GalleryImage")

  @@index([sectionId])
  @@map("images")
}

model Video {
  id        String   @id @default(uuid())
  src       String   @default("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
  alt       String   @default("Placeholder Video")
  caption   String?
  width     String?
  height    String?
  sectionId String   @unique @map("sectionId")
  section   Section  @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([sectionId])
  @@map("videos")
}

model TextImage {
  id        String   @id @default(uuid())
  textId    String   @unique @map("textId")
  imageId   String   @unique @map("imageId")
  text      Text     @relation(fields: [textId], references: [id], onDelete: Cascade)
  image     Image    @relation(fields: [imageId], references: [id], onDelete: Cascade)
  sectionId String   @unique @map("sectionId")
  section   Section  @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([textId])
  @@index([imageId])
  @@index([sectionId])
  @@map("text_images")
}

model Social {
  id        String   @id @default(uuid())
  sectionId String   @unique @map("sectionId")
  platform  String   @default("twitter")
  href      String   @default("https://twitter.com/viber")
  icon      String   @default("https://www.svgrepo.com/show/475437/twitter.svg")
  label     String   @default("Twitter")
  color     String   @default("blue")
  size      String   @default("24")
  position  String   @default("left")
  section   Section  @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([sectionId])
  @@map("socials")
}

model Map {
  id              String   @id @default(uuid())
  sectionId       String   @unique @map("sectionId")
  section         Section  @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  latitude        Float?   @default(0.0)
  longitude       Float?   @default(0.0)
  zoom            Int?     @default(10)
  address         String?
  city            String?
  state           String?
  country         String?
  pinCode         String?
  pin             Boolean? @default(true)
  pinColor        String?  @default("red")
  pinSize         String?  @default("24")
  pinLabel        String?
  pinIcon         String?  @default("https://www.svgrepo.com/show/475437/twitter.svg")
  iframe          Boolean? @default(true)
  height          String?  @default("400")
  width           String?  @default("100%")
  style           String?
  iframeSrc       String?
  googleApiKey    String?
  googleMapId     String?
  googleMapUrl    String?
  googleMapStyle  String?
  googleMapType   String?  @default("roadmap")
  googleMapZoom   Int?     @default(10)
  googleMapCenter String?  @default("0,0")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([sectionId])
  @@map("maps")
}

model Gallery {
  id           String        @id @default(uuid())
  sectionId    String        @unique @map("sectionId")
  section      Section       @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  images       Image[]       @relation("GalleryImage")
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  GalleryImage GalleryImage?
  Image        Image[]

  @@index([sectionId])
  @@map("galleries")
}

model GalleryImage {
  id        String   @id @default(uuid())
  galleryId String   @unique @map("galleryId")
  imageId   String   @unique @map("imageId")
  gallery   Gallery  @relation(fields: [galleryId], references: [id], onDelete: Cascade)
  image     Image    @relation(fields: [imageId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([galleryId])
  @@index([imageId])
  @@map("gallery_images")
}

// model Form {
//   id        String   @id @default(uuid())
//   sectionId String   @unique @map("sectionId")
//   section   Section  @relation(fields: [sectionId], references: [id], onDelete: Cascade)
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   @@index([sectionId])
//   @@map("forms")
// }

// model Ecommerce {
//   id        String   @id @default(uuid())
//   sectionId String   @unique @map("sectionId")
//   section   Section  @relation(fields: [sectionId], references: [id], onDelete: Cascade)
//   createdAt DateTime @default(now())
//   updatedAt DateTime @updatedAt

//   @@index([sectionId])
//   @@map("ecommerces")
// }
