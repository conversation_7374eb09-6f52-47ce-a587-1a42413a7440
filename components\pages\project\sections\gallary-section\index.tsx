import React from "react";
import { Section, Gallery, Image as ImageType } from "@prisma/client";
import Image from "next/image";

type Props = {
  section: Section & {
    gallary?:
      | (Gallery & {
          images: ImageType[];
        })
      | null;
  };
};

const GallarySection = ({ section }: Props) => {
  return (
    <div>
      {section.gallary?.images.map((image) => (
        <Image
          key={image.id}
          src={image.src}
          alt={image.alt}
          width={image.width ? Number(image.width) || 500}
          height={image.height ? Number(image.width) || 500}
        />
      ))}
    </div>
  );
};

export default GallarySection;
