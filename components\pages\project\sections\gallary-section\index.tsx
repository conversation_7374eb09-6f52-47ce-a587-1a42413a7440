import React from "react";
import { Section, Gallery } from "@prisma/client";

type Props = {
  section: Section & {
    gallary?: Gallery | null;
  };
};

const GallarySection = ({ section }: Props) => {
  return (
    <div>
      {section.gallary?.images.map((image) => (
        <img
          key={image.id}
          src={image.src}
          alt={image.alt}
          width={image.width}
          height={image.height}
        />
      ))}
    </div>
  );
};

export default GallarySection;
