import React from "react";
import { Section, Video } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { Edit2Icon } from "lucide-react";
import VideoForm from "./video-form";

type Props = {
  section: Section & {
    video?: Video | null;
  };
};

const VideoSection = ({ section }: Props) => {
  if (!section.video) {
    return null;
  }

  return (
    <div className="relative flex flex-col items-center group/section">
      <VideoForm sectionId={section.id} video={section.video}>
        <div className="absolute inset-0 h-24  group-hover/section:opacity-100 transition-colors duration-300 flex items-center justify-center">
          <Button
            variant="secondary"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 gap-2"
          >
            <Edit2Icon className="w-4 h-4" />
            Edit Video
          </Button>
        </div>
      </VideoForm>
      <iframe
        width={section.video?.width ? Number(section.video.width) : 500}
        height={section.video?.height ? Number(section.video.height) : 500}
        src={section.video?.src as string}
        frameBorder={"0"}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerPolicy={"strict-origin-when-cross-origin"}
        allowFullScreen
      ></iframe>
      {section.video?.caption && (
        <figcaption className="text-sm text-muted-foreground mt-8">
          {section.video?.caption}
        </figcaption>
      )}
    </div>
  );
};

export default VideoSection;
