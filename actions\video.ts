"use server";

import { ActionState, StatusCode } from "@/lib/types";
import {
  createVideo,
  deleteVideo,
  isVideo,
  updateVideo,
} from "@/queries/videos";
import { Video } from "@prisma/client";
import { revalidatePath } from "next/cache";

type CreateVideoProps = {
  sectionId: Video["sectionId"];
  src?: Video["src"];
  alt?: Video["alt"];
  caption?: Video["caption"];
  width?: Video["width"];
  height?: Video["height"];
};

type UpdateVideoProps = {
  id: Video["id"];
  src?: Video["src"];
  alt?: Video["alt"];
  caption?: Video["caption"];
  width?: Video["width"];
  height?: Video["height"];
};

export const createVideoAction = async ({
  sectionId,
  src,
  alt,
  caption,
  width,
  height,
}: CreateVideoProps): Promise<ActionState<Video>> => {
  let video;
  try {
    video = await createVideo({ sectionId, src, alt, caption, width, height });
    return {
      code: StatusCode.Created,
      message: "Video created successfully",
      data: video as Video,
      success: true,
    };
  } catch (error) {
    console.error("Error creating video:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while creating the video",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${video?.section?.projectId}`);
  }
};

export const updateVideoAction = async ({
  id,
  src,
  alt,
  caption,
  width,
  height,
}: UpdateVideoProps): Promise<ActionState<Video>> => {
  let video;
  try {
    const isVideoExists = await isVideo(id);

    if (!isVideoExists) {
      return {
        code: StatusCode.NotFound,
        message: "Video not found",
        success: false,
      };
    }

    video = await updateVideo({ id, src, alt, caption, width, height });
    return {
      code: StatusCode.Ok,
      message: "Video updated successfully",
      data: video as Video,
      success: true,
    };
  } catch (error) {
    console.error("Error updating video:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while updating the video",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${video?.section?.projectId}`);
  }
};

export const deleteVideoAction = async (
  id: Video["id"]
): Promise<ActionState<Video>> => {
  let video;
  try {
    const isVideoExists = await isVideo(id);

    if (!isVideoExists) {
      return {
        code: StatusCode.NotFound,
        message: "Video not found",
        success: false,
      };
    }

    video = await deleteVideo(id);
    return {
      code: StatusCode.Ok,
      message: "Video deleted successfully",
      data: video as Video,
      success: true,
    };
  } catch (error) {
    console.error("Error deleting video:", error);
    return {
      code: StatusCode.InternalServerError,
      message: "Something went wrong while deleting the video",
      error: error as Error,
      success: false,
    };
  } finally {
    revalidatePath(`/project/${video?.section?.projectId}`);
  }
};
